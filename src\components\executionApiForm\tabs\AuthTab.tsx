import React, { useEffect } from 'react';
import { Control, Controller, UseFormSetValue, UseFormGetValues } from 'react-hook-form';
import { Box, Flex, Divider } from '@mantine/core';
import { KanbanSelect, KanbanTextInput } from 'kanban-design-system';
import { ExecutionApiInfoModel } from '@models/ExecutionApiInfoModel';
import { AuthTypeEnum, AuthTypeLabel } from '@common/constants/ExecutionConstants';
import ParamAwareInput from '@pages/admins/executionConfig/tabs/execution/components/ParamAwareInput';
import { generateAuthHeader, upsertAutoHeader } from '@common/utils/ExecutionApiUtils';
import { EXECUTION_API_AUTHORIZATION } from '@pages/admins/executionConfig/Constants';
import {
  EXECUTION_API_PASSWORD_MAX_LENGTH,
  EXECUTION_API_TOKEN_MAX_LENGTH,
  EXECUTION_API_USERNAME_MAX_LENGTH,
} from '@common/constants/ValidationConstant';

interface AuthTabProps {
  control: Control<{ apiInfo: ExecutionApiInfoModel }>;
  setValue: UseFormSetValue<{ apiInfo: ExecutionApiInfoModel }>;
  getValues: UseFormGetValues<{ apiInfo: ExecutionApiInfoModel }>;
  headersFA: {
    fields: any[];
    append: (value: any) => void;
    remove: (index: number) => void;
  };
  isViewMode?: boolean;
  isEditMode?: boolean;
  variableNames?: string[];
  onFieldChange?: () => void;
}

const AuthTab: React.FC<AuthTabProps> = ({
  control,
  getValues,
  headersFA,
  isEditMode = false,
  isViewMode = false,
  onFieldChange,
  setValue,
  variableNames = [],
}) => {
  const authType = getValues('apiInfo.authentication.authType');

  // Auto-generate Authorization header when auth changes
  useEffect(() => {
    if (isViewMode) {
      return;
    }

    const authData = getValues('apiInfo.authentication');
    const authHeader = generateAuthHeader(authData);

    if (authHeader) {
      upsertAutoHeader({
        key: EXECUTION_API_AUTHORIZATION,
        value: authHeader,
        headersFA,
        setValue,
      });
    } else {
      // Remove auto-generated auth header if no auth
      const existing = headersFA.fields.findIndex((h) => h.key?.toLowerCase() === EXECUTION_API_AUTHORIZATION.toLowerCase() && h.autoGenerated);
      if (existing >= 0) {
        headersFA.remove(existing);
      }
    }
  }, [authType, getValues, setValue, headersFA, isViewMode]);

  const handleAuthTypeChange = (value: AuthTypeEnum | null) => {
    setValue('apiInfo.authentication.authType', value || AuthTypeEnum.NONE, { shouldDirty: true });

    // Clear auth fields when changing type
    if (value !== AuthTypeEnum.BASIC) {
      setValue('apiInfo.authentication.username', '', { shouldDirty: true });
      setValue('apiInfo.authentication.password', '', { shouldDirty: true });
    }
    if (value !== AuthTypeEnum.BEARER) {
      setValue('apiInfo.authentication.token', '', { shouldDirty: true });
    }

    onFieldChange?.();
  };

  return (
    <Box p='md'>
      <Flex align='center' gap='md' mb='md'>
        <Controller
          name='apiInfo.authentication.authType'
          control={control}
          render={({ field }) => (
            <KanbanSelect
              {...field}
              label='Type'
              disabled={isViewMode || isEditMode}
              data={Object.entries(AuthTypeLabel).map(([value, label]) => ({
                value,
                label,
              }))}
              value={field.value || AuthTypeEnum.NONE}
              onChange={handleAuthTypeChange}
              allowDeselect={false}
              w={150}
            />
          )}
        />

        <Divider orientation='vertical' size='sm' />

        {authType === AuthTypeEnum.BASIC && (
          <Flex direction='column' gap='sm' flex={1}>
            <Controller
              name='apiInfo.authentication.username'
              control={control}
              render={({ field }) =>
                isViewMode ? (
                  <KanbanTextInput {...field} label='Username' disabled value={field.value || ''} />
                ) : (
                  <ParamAwareInput
                    field={{
                      ...field,
                      value: field.value || '',
                      onChange: (e) => {
                        field.onChange(e.target.value);
                        onFieldChange?.();
                      },
                    }}
                    disabled={isViewMode}
                    executionParams={variableNames}
                    maxLength={EXECUTION_API_USERNAME_MAX_LENGTH}
                  />
                )
              }
            />
            <Controller
              name='apiInfo.authentication.password'
              control={control}
              render={({ field }) =>
                isViewMode ? (
                  <KanbanTextInput {...field} label='Password' type='password' disabled value={field.value || ''} />
                ) : (
                  <ParamAwareInput
                    field={{
                      ...field,
                      value: field.value || '',
                      onChange: (e) => {
                        field.onChange(e.target.value);
                        onFieldChange?.();
                      },
                    }}
                    disabled={isViewMode}
                    executionParams={variableNames}
                    maxLength={EXECUTION_API_PASSWORD_MAX_LENGTH}
                  />
                )
              }
            />
          </Flex>
        )}

        {authType === AuthTypeEnum.BEARER && (
          <Controller
            name='apiInfo.authentication.token'
            control={control}
            render={({ field }) =>
              isViewMode ? (
                <KanbanTextInput {...field} label='Token' disabled value={field.value || ''} flex={1} />
              ) : (
                <ParamAwareInput
                  field={{
                    ...field,
                    value: field.value || '',
                    onChange: (e) => {
                      field.onChange(e.target.value);
                      onFieldChange?.();
                    },
                  }}
                  disabled={isViewMode}
                  executionParams={variableNames}
                  maxLength={EXECUTION_API_TOKEN_MAX_LENGTH}
                />
              )
            }
          />
        )}
      </Flex>
    </Box>
  );
};

export default AuthTab;
